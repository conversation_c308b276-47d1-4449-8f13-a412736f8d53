# Cron job configuration for fixing failed transliterations
# Add this to your existing config/schedule.rb file or use whenever gem

# Option 1: One-time fix (run once and remove)
# Uncomment the line below to run the fix script once
# every 1.day, at: '2:00 am' do
#   runner "load 'lib/scripts/fix_failed_transliterations.rb'"
# end

# Option 2: Weekly maintenance (recommended for ongoing monitoring)
# Runs every Sunday at 3 AM to catch any new failed transliterations
every 1.week, at: '3:00 am' do
  runner "load 'lib/scripts/fix_failed_transliterations.rb'"
end

# Option 3: Daily monitoring (for high-volume systems)
# Uncomment if you want daily checks
# every 1.day, at: '3:00 am' do
#   runner "load 'lib/scripts/fix_failed_transliterations.rb'"
# end

# Verification job - runs monthly to generate reports
every 1.month, at: '2:00 am' do
  runner "load 'lib/scripts/verify_failed_transliterations.rb'"
end
