# Transliteration System Guide

## Overview

The transliteration system in this repository provides automatic translation capabilities between multiple Indian languages and English. It's designed to handle multilingual content seamlessly, particularly focusing on Telugu and English translations, with support for 8 additional Indian languages.

## Architecture

### Core Components

1. **Transliteration Module** (`app/modules/transliteration/`)
   - `Model` - Ruby concern for ActiveRecord models
   - `Text` - High-level text processing interface
   - `Word` - Individual word processing and language detection

2. **Entity Storage** (`app/models/entity_name.rb`)
   - Polymorphic model storing translated names
   - Stores `name_en` (English) and `name_te` (Telugu) versions

3. **Background Processing** (`app/workers/`)
   - `TransliterateEntityName` - Main transliteration worker
   - `TransliterateUserRoleOnRoleUpdate` - Batch processing for role updates

## How It Works

### 1. Language Detection

The system uses Unicode character ranges to detect languages:

```ruby
# Telugu detection (Unicode range: U+0C00-U+0C7F)
def telugu?(text)
  pattern = /[\u0C00-\u0C7F]/
  matching_chars = text.chars.count { |char| char =~ pattern }
  (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
end
```

**Supported Languages:**
- English (`en`) - Latin characters and numbers
- Telugu (`te`) - Unicode range: U+0C00-U+0C7F
- Hindi (`hi`) - Unicode range: U+0900-U+097F, U+A8E0-U+A8FF
- Tamil (`ta`) - Unicode range: U+0B80-U+0BFF
- Kannada (`kn`) - Unicode range: U+0C80-U+0CFF
- Malayalam (`ml`) - Unicode range: U+0D00-U+0D7F
- Punjabi (`pa`) - Unicode range: U+0A00-U+0A7F
- Oriya (`or`) - Unicode range: U+0B00-U+0B7F
- Bengali (`bn`) - Unicode range: U+0980-U+09FF

**Detection Threshold:** 80% of characters must match the language pattern.

### 2. Translation Process

The system uses AWS Translate service for actual translation:

```ruby
def transliterate(target_language_code)
  translator = Aws::Translate::Client.new(
    region: 'ap-south-1',
    access_key_id: Rails.application.credentials.aws_access_key_id,
    secret_access_key: Rails.application.credentials.aws_secret_access_key
  )
  
  response = translator.translate_text({
    text: @text,
    source_language_code: language,
    target_language_code: target_language_code,
  })
  
  response.translated_text
end
```

**Current Limitation:** Only supports translation between English and other languages (not direct regional language to regional language).

### 3. Text Processing Flow

1. **Text Input** → Split into words
2. **Language Detection** → Each word analyzed for language
3. **Translation** → AWS Translate API called for each word
4. **Reconstruction** → Words rejoined with spaces

## Ruby Principles Integration

### 1. Concern Pattern (ActiveSupport::Concern)

The transliteration functionality is implemented as a Ruby concern:

```ruby
module Transliteration
  module Model
    def transliteration(name_attr: :name, force_update_on_commit: false)
      mod = Module.new do
        extend ActiveSupport::Concern
        # ... implementation
      end
      include mod
    end
  end
end
```

**Benefits:**
- **Modularity**: Reusable across multiple models
- **Separation of Concerns**: Keeps transliteration logic separate
- **Configuration**: Flexible attribute and behavior configuration

### 2. Polymorphic Associations

Uses Rails polymorphic associations for flexible entity storage:

```ruby
# EntityName model
belongs_to :entity, polymorphic: true

# In models using transliteration
has_one :entity_name, as: :entity, autosave: true
```

### 3. Delegation Pattern

Provides clean interface through delegation:

```ruby
delegate :name_en, :name_te, to: :entity_name, prefix: false, allow_nil: true
```

### 4. Callback Hooks

Automatic transliteration triggered by ActiveRecord callbacks:

```ruby
after_commit do
  if force_update_on_commit || saved_change_to_attribute?(self.name_attr)
    TransliterateEntityName.perform_async(self.class.name, id)
  end
end
```

## Entity Integration

### 1. User Model

```ruby
class User < ApplicationRecord
  transliteration name_attr: :name
  # Automatically transliterates user names
end
```

**Usage:**
- Transliterates user names when created/updated
- Provides `user.name_en` and `user.name_te` accessors
- Triggers background job for AWS translation

### 2. UserRole Model (Complex Example)

```ruby
class UserRole < ApplicationRecord
  transliteration name_attr: :get_description, force_update_on_commit: true
  
  def get_description
    # Complex logic building role descriptions from:
    # - Role name
    # - Parent circle name  
    # - Purview circle name
    # - Free text (if allowed)
    # - Display order configuration
  end
end
```

**Special Features:**
- Uses method instead of simple attribute
- Force updates on every commit
- Handles complex badge descriptions
- Overrides reindex to update user search data

### 3. Role Model Integration

```ruby
class Role < ApplicationRecord
  after_update_commit :trigger_user_roles_transliteration
  
  def trigger_user_roles_transliteration
    if saved_change_to_name? || saved_change_to_parent_circle_id?
      TransliterateUserRoleOnRoleUpdate.perform_async(id)
    end
  end
end
```

**Cascade Updates:** When roles change, all associated user roles get retransliterated.

## Usage Patterns

### 1. Simple Name Transliteration

```ruby
class Circle < ApplicationRecord
  transliteration name_attr: :name
end

# Usage
circle = Circle.create(name: "తెలంగాణ")
circle.name_en  # → "Telangana" (via AWS Translate)
circle.name_te  # → "తెలంగాణ"
```

### 2. Complex Description Transliteration

```ruby
# UserRole badge descriptions
user_role.get_description  # → "తెలంగాణ సభ్యుడు"
user_role.name_en         # → "Telangana Member"
user_role.name_te         # → "తెలంగాణ సభ్యుడు"
```

### 3. Manual Transliteration

```ruby
# Direct usage without model integration
text = Transliteration::Text.new("Hello World")
telugu_text = text.transliterate('te')  # → "హలో వరల్డ్"
```

## Background Processing

### 1. Asynchronous Processing

All transliteration happens in background jobs to avoid blocking user requests:

```ruby
# Low priority queue with throttling
class TransliterateEntityName
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 1
  sidekiq_throttle(threshold: { limit: 5, period: 1.second })
end
```

### 2. Batch Processing

When roles update, all related user roles are processed in batches:

```ruby
def perform(id)
  UserRole.where(role_id: id).select(:id).find_each(batch_size: 500) do |user_role|
    TransliterateEntityName.perform_async("UserRole", user_role.id)
  end
end
```

## Best Practices Followed

### 1. **Graceful Degradation**
- Returns original text if translation fails
- Handles unsupported languages gracefully
- Logs errors without breaking functionality

### 2. **Performance Optimization**
- Background processing prevents UI blocking
- Throttling prevents API rate limit issues
- Batch processing for bulk updates

### 3. **Flexible Configuration**
- Configurable source attributes
- Optional force update behavior
- Customizable callback triggers

### 4. **Error Handling**
- AWS API error handling
- Language detection fallbacks
- Comprehensive logging

### 5. **Testing Coverage**
- Unit tests for language detection
- Integration tests for model behavior
- Mocked AWS services in tests

## Configuration

### Environment Setup

```ruby
# config/credentials.yml.enc
aws_access_key_id: your_aws_key
aws_secret_access_key: your_aws_secret

# AWS region configured in transliteration code
region: 'ap-south-1'
```

### Model Configuration Options

```ruby
# Basic usage
transliteration name_attr: :name

# Force update on every commit (useful for computed fields)
transliteration name_attr: :get_description, force_update_on_commit: true

# Custom attribute
transliteration name_attr: :title
```

This system provides a robust, scalable solution for multilingual content management while following Ruby and Rails best practices for modularity, performance, and maintainability.

## Detailed Implementation Examples

### 1. User Name Transliteration

```ruby
# When a user is created or updated
user = User.create(name: "రాజేష్")

# Automatic background processing triggered
# TransliterateEntityName.perform_async("User", user.id)

# After processing:
user.reload
user.name     # → "రాజేష్" (original)
user.name_en  # → "Rajesh" (translated)
user.name_te  # → "రాజేష్" (original Telugu)
```

### 2. UserRole Badge Description Transliteration

```ruby
# Complex badge description building
user_role = UserRole.create(
  role: Role.find_by(name: "Member"),
  parent_circle: Circle.find_by(name: "తెలంగాణ రాష్ట్ర కాంగ్రెస్"),
  purview_circle: Circle.find_by(name: "హైదరాబాద్")
)

# get_description method builds: "హైదరాబాద్ Member"
user_role.get_description  # → "హైదరాబాద్ Member"

# After transliteration:
user_role.name_en  # → "Hyderabad Member"
user_role.name_te  # → "హైదరాబాద్ సభ్యుడు"
```

### 3. Circle Creation with Transliteration

```ruby
# From CreateCircleFlow worker
name = Transliteration::Text.new(user.name)

circle = Circle.create(
  name: name.transliterate('te'),      # Telugu version
  name_en: name.transliterate('en'),   # English version
  circle_type: :interest,
  level: :political_leader
)
```

## Database Schema

### EntityName Table Structure

```sql
CREATE TABLE entity_names (
  id BIGINT PRIMARY KEY,
  entity_type VARCHAR NOT NULL,  -- Polymorphic type (User, UserRole, etc.)
  entity_id BIGINT NOT NULL,     -- Polymorphic ID
  name_en TEXT,                  -- English translation
  name_te TEXT,                  -- Telugu translation
  created_at TIMESTAMP,
  updated_at TIMESTAMP,

  INDEX(entity_type, entity_id)  -- Polymorphic index
);
```

## Advanced Features

### 1. Search Integration

The system integrates with Elasticsearch for multilingual search:

```ruby
def update_entity_name!
  # ... transliteration logic ...

  # Trigger search reindexing if model supports it
  return unless respond_to?(:reindex)
  Rails.logger.debug("Indexing(mode: queue) #{self.class.name} with #{id} after transliteration")
  reindex(mode: :queue)
end
```

### 2. Ex-Role Handling

Special logic for handling ex-roles with language-specific prefixes:

```ruby
def get_role_name
  text = role.name
  end_date = self.end_date.present? ? self.end_date : UserRole::DEFAULT_USER_ROLE_END_DATE

  # Add appropriate prefix for ex-roles
  if Date.parse(Time.zone.today.to_s) > end_date
    # English text gets "Ex-", Telugu text gets "మాజీ "
    text = text.match?(/\A[a-zA-Z]/) ? "Ex-" + text : "మాజీ " + text
  end
  text
end
```

### 3. Cascade Updates

When core entities change, related entities are automatically retransliterated:

```ruby
# Role model
def trigger_user_roles_transliteration
  if saved_change_to_name? || saved_change_to_parent_circle_id? || saved_change_to_purview_level?
    TransliterateUserRoleOnRoleUpdate.perform_async(id)
  end
end

# Circle model
def trigger_identity_image_generation_for_users
  if saved_change_to_name? || saved_change_to_short_name?
    user_roles_with_circle = UserRole.where(parent_circle_id: id).or(UserRole.where(purview_circle_id: id))
    # Batch process all affected user roles
  end
end
```

## Monitoring and Debugging

### 1. Logging

Comprehensive logging throughout the transliteration process:

```ruby
Rails.logger.debug("Checking #{self.class.name} with #{id} for update on #{self.name_attr}")
Rails.logger.debug("Language not detected for #{@text}")
Rails.logger.error("AWS Translate error: #{e.message}")
```

### 2. Error Handling

Graceful error handling with fallbacks:

```ruby
begin
  response = translator.translate_text(params)
  return response.translated_text if response.present?
rescue Aws::Translate::Errors::ServiceError => e
  Rails.logger.error("AWS Translate error: #{e.message}")
end
@text  # Return original text on error
```

## Testing Strategy

### 1. Unit Tests

Language detection and translation logic:

```ruby
RSpec.describe Transliteration::Word do
  it 'should detect telugu text' do
    text = Transliteration::Word.new('తెలుగు')
    expect(text.language).to eq(Transliteration::LANGUAGE_CODE_TELUGU)
  end

  it 'should transliterate from telugu to english' do
    # Mock AWS response
    translate_response = double('translate_response', translated_text: 'telugu')
    allow(aws_translate_client).to receive(:translate_text).and_return(translate_response)

    text = Transliteration::Word.new('తెలుగు')
    expect(text.transliterate('en')).to eq('telugu')
  end
end
```

### 2. Integration Tests

Model behavior and worker functionality:

```ruby
RSpec.describe TransliterateEntityName do
  it 'updates entity name for user' do
    user = FactoryBot.create(:user, name: 'test')
    described_class.new.perform(User.name, user.id)
    user.reload
    expect(user.entity_name.name_en).to eq('test')
  end
end
```

## Performance Considerations

### 1. Background Processing
- All transliteration happens asynchronously
- Prevents blocking user interactions
- Uses low-priority queue to avoid impacting critical operations

### 2. Rate Limiting
- Sidekiq throttling prevents AWS API rate limit issues
- Configurable limits: 5 requests per second

### 3. Batch Processing
- Bulk updates processed in batches of 500
- Prevents memory issues with large datasets

### 4. Caching Strategy
- EntityName records cached at model level
- Search index updated after transliteration
- User cache invalidated when names change

## Future Enhancements

### 1. Direct Regional Language Translation
Currently limited to English as intermediary language. Future enhancement could support direct Telugu→Hindi translation.

### 2. Additional Languages
Framework supports adding more languages by:
- Adding Unicode range detection methods
- Adding language codes
- Configuring AWS Translate language pairs

### 3. Caching Layer
Could add Redis caching for frequently translated terms to reduce AWS API calls.

### 4. Translation Quality Scoring
Could implement confidence scoring and manual review workflows for critical translations.
