#!/usr/bin/env ruby

# Script to fix EntityName records where transliteration failed
# These are records where name_en = name_te but contain actual text that should be translated

puts "Starting failed transliteration fix script..."
puts "Time: #{Time.current}"

# Define regex pattern to match alphabetic characters (English + Indian languages)
# Includes: English, Hindi, Punjabi, Oriya, Tamil, Telugu, Kannada, Malayalam, Bengali
alphabetic_pattern = '[a-zA-Z\u0900-\u097F\u0A00-\u0A7F\u0B00-\u0B7F\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0D00-\u0D7F\u0980-\u09FF]'

# Find problematic EntityName records
problematic_records = EntityName.where("name_en = name_te")
  .where("name_en REGEXP ?", alphabetic_pattern)

total_count = problematic_records.count
puts "Found #{total_count} EntityName records that need re-transliteration"

if total_count == 0
  puts "No problematic records found. Exiting."
  exit
end

# Show distribution by entity type
puts "\nDistribution by entity type:"
distribution = problematic_records.group(:entity_type).count
distribution.each do |entity_type, count|
  puts "  #{entity_type}: #{count} records"
end

# Process records in batches
batch_size = 500
processed_count = 0
failed_count = 0

puts "\nStarting batch processing (batch size: #{batch_size})..."

problematic_records.find_each(batch_size: batch_size) do |entity_name|
  begin
    # Get the actual entity (User, UserRole, etc.)
    entity = entity_name.entity
    
    if entity.present?
      # Enqueue transliteration job
      TransliterateEntityName.perform_async(entity.class.name, entity.id)
      processed_count += 1
      
      # Log progress every 100 records
      if processed_count % 100 == 0
        puts "Processed #{processed_count}/#{total_count} records..."
      end
    else
      puts "Warning: EntityName ID #{entity_name.id} has no associated entity (#{entity_name.entity_type}##{entity_name.entity_id})"
      failed_count += 1
    end
    
  rescue => e
    puts "Error processing EntityName ID #{entity_name.id}: #{e.message}"
    failed_count += 1
  end
end

puts "\n" + "="*50
puts "SUMMARY"
puts "="*50
puts "Total problematic records found: #{total_count}"
puts "Successfully enqueued for re-transliteration: #{processed_count}"
puts "Failed to process: #{failed_count}"
puts "Completion time: #{Time.current}"

if processed_count > 0
  puts "\nNote: Transliteration jobs have been enqueued in Sidekiq."
  puts "Monitor the 'low' queue for processing progress."
  puts "Jobs are throttled to 5 per second to avoid AWS API limits."
end

puts "\nScript completed successfully!"
