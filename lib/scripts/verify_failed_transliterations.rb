#!/usr/bin/env ruby

# Verification script to analyze EntityName records where transliteration failed
# Run this BEFORE the fix script to understand the scope of the problem

puts "Analyzing failed transliteration records..."
puts "Time: #{Time.current}"
puts "="*60

# Define regex pattern to match alphabetic characters (English + Indian languages)
alphabetic_pattern = '[a-zA-Z\u0900-\u097F\u0A00-\u0A7F\u0B00-\u0B7F\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0D00-\u0D7F\u0980-\u09FF]'

# 1. Count all EntityName records where name_en = name_te
same_name_count = EntityName.where("name_en = name_te").count
puts "1. Total EntityName records where name_en = name_te: #{same_name_count}"

# 2. Count records that are only numeric or special characters (these might be legitimate)
numeric_special_only = EntityName.where("name_en = name_te")
  .where("name_en NOT REGEXP ?", alphabetic_pattern)
  .count
puts "2. Records with only numeric/special characters (might be OK): #{numeric_special_only}"

# 3. Count problematic records (contain letters but weren't translated)
problematic_count = EntityName.where("name_en = name_te")
  .where("name_en REGEXP ?", alphabetic_pattern)
  .count
puts "3. PROBLEMATIC records (contain letters but not translated): #{problematic_count}"

puts "\n" + "="*60
puts "DETAILED ANALYSIS"
puts "="*60

# 4. Distribution by entity type for problematic records
puts "\n4. Distribution of problematic records by entity type:"
problematic_distribution = EntityName.where("name_en = name_te")
  .where("name_en REGEXP ?", alphabetic_pattern)
  .group(:entity_type)
  .count

problematic_distribution.each do |entity_type, count|
  percentage = (count.to_f / problematic_count * 100).round(2)
  puts "   #{entity_type}: #{count} records (#{percentage}%)"
end

# 5. Sample problematic records
puts "\n5. Sample problematic records (first 10):"
sample_records = EntityName.where("name_en = name_te")
  .where("name_en REGEXP ?", alphabetic_pattern)
  .limit(10)

sample_records.each_with_index do |record, index|
  puts "   #{index + 1}. ID: #{record.id}"
  puts "      Entity: #{record.entity_type}##{record.entity_id}"
  puts "      Text: '#{record.name_en}'"
  puts "      Created: #{record.created_at}"
  puts ""
end

# 6. Check for recent vs old records
puts "6. Age analysis of problematic records:"
recent_count = EntityName.where("name_en = name_te")
  .where("name_en REGEXP ?", alphabetic_pattern)
  .where("created_at > ?", 30.days.ago)
  .count

old_count = problematic_count - recent_count

puts "   Recent (last 30 days): #{recent_count} records"
puts "   Older (30+ days): #{old_count} records"

# 7. Check if entities still exist
puts "\n7. Checking entity existence for sample records..."
orphaned_count = 0
sample_records.limit(50).each do |entity_name|
  begin
    entity = entity_name.entity
    if entity.nil?
      orphaned_count += 1
    end
  rescue => e
    orphaned_count += 1
  end
end

puts "   Orphaned records in sample of 50: #{orphaned_count}"
if orphaned_count > 0
  puts "   Warning: Some EntityName records point to non-existent entities"
end

puts "\n" + "="*60
puts "SUMMARY & RECOMMENDATIONS"
puts "="*60

puts "Total records to fix: #{problematic_count}"
puts "Estimated processing time: #{(problematic_count / 300.0).ceil} minutes"
puts "(Based on 5 jobs/second throttling = ~300 jobs/minute)"

if problematic_count > 100000
  puts "\nWARNING: Large number of records detected!"
  puts "Consider running the fix script during off-peak hours."
end

puts "\nTo proceed with the fix:"
puts "1. Review the sample records above"
puts "2. Run: rails runner lib/scripts/fix_failed_transliterations.rb"
puts "3. Monitor Sidekiq 'low' queue for progress"

puts "\nVerification completed at: #{Time.current}"
