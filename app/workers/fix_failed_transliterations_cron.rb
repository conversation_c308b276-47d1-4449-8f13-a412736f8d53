class FixFailedTransliterationsCron
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 1

  def perform
    Rails.logger.info("Starting FixFailedTransliterationsCron at #{Time.current}")
    
    # Define patterns for plain language fonts supported by AWS Translate
    # Excludes fancy Unicode fonts like combining diacritical marks
    plain_language_pattern = build_plain_language_pattern
    
    # Find EntityName records where name_en = name_te and contain plain language text
    problematic_records = EntityName.where("name_en = name_te")
      .where("name_en REGEXP ?", plain_language_pattern)
      .where("name_en NOT REGEXP ?", fancy_unicode_pattern)
    
    total_count = problematic_records.count
    Rails.logger.info("Found #{total_count} EntityName records that need re-transliteration")
    
    return if total_count == 0
    
    # Log distribution by entity type
    distribution = problematic_records.group(:entity_type).count
    Rails.logger.info("Distribution by entity type: #{distribution}")
    
    # Process records in batches
    batch_size = 500
    processed_count = 0
    failed_count = 0
    
    problematic_records.find_each(batch_size: batch_size) do |entity_name|
      begin
        # Skip if text contains fancy Unicode characters
        next if contains_fancy_unicode?(entity_name.name_en)
        
        # Get the actual entity (User, UserRole, etc.)
        entity = entity_name.entity
        
        if entity.present?
          # Enqueue transliteration job
          TransliterateEntityName.perform_async(entity.class.name, entity.id)
          processed_count += 1
          
          # Log progress every 1000 records
          if processed_count % 1000 == 0
            Rails.logger.info("Processed #{processed_count}/#{total_count} records...")
          end
        else
          Rails.logger.warn("EntityName ID #{entity_name.id} has no associated entity (#{entity_name.entity_type}##{entity_name.entity_id})")
          failed_count += 1
        end
        
      rescue => e
        Rails.logger.error("Error processing EntityName ID #{entity_name.id}: #{e.message}")
        failed_count += 1
      end
    end
    
    Rails.logger.info("FixFailedTransliterationsCron completed:")
    Rails.logger.info("- Total problematic records found: #{total_count}")
    Rails.logger.info("- Successfully enqueued for re-transliteration: #{processed_count}")
    Rails.logger.info("- Failed to process: #{failed_count}")
    Rails.logger.info("- Completion time: #{Time.current}")
  end
  
  private
  
  def build_plain_language_pattern
    # Pattern for plain language characters supported by AWS Translate
    # Includes basic Latin and Indian language scripts without combining marks
    [
      '[a-zA-Z0-9]',                    # Basic Latin letters and numbers
      '[\u0900-\u097F]',                # Hindi (Devanagari)
      '[\u0A00-\u0A7F]',                # Punjabi (Gurmukhi)
      '[\u0B00-\u0B7F]',                # Oriya
      '[\u0B80-\u0BFF]',                # Tamil
      '[\u0C00-\u0C7F]',                # Telugu
      '[\u0C80-\u0CFF]',                # Kannada
      '[\u0D00-\u0D7F]',                # Malayalam
      '[\u0980-\u09FF]'                 # Bengali
    ].join('|')
  end
  
  def fancy_unicode_pattern
    # Pattern to detect fancy Unicode characters that should be avoided
    # Includes combining diacritical marks, mathematical symbols, etc.
    [
      '[\u0300-\u036F]',                # Combining Diacritical Marks (like S͜͡u͜͡b͜͡h͜͡a͜͡n͜͡i͜͡)
      '[\u1AB0-\u1AFF]',                # Combining Diacritical Marks Extended
      '[\u1DC0-\u1DFF]',                # Combining Diacritical Marks Supplement
      '[\u20D0-\u20FF]',                # Combining Diacritical Marks for Symbols
      '[\u2100-\u214F]',                # Letterlike Symbols
      '[\u2190-\u21FF]',                # Arrows
      '[\u2200-\u22FF]',                # Mathematical Operators
      '[\u2300-\u23FF]',                # Miscellaneous Technical
      '[\u2460-\u24FF]',                # Enclosed Alphanumerics
      '[\u25A0-\u25FF]',                # Geometric Shapes
      '[\u2600-\u26FF]',                # Miscellaneous Symbols
      '[\u2700-\u27BF]',                # Dingbats
      '[\uFE20-\uFE2F]',                # Combining Half Marks
      '[\uFE30-\uFE4F]'                 # CJK Compatibility Forms
    ].join('|')
  end
  
  def contains_fancy_unicode?(text)
    return false if text.blank?
    
    # Check if text contains any fancy Unicode characters
    text.match?(/#{fancy_unicode_pattern}/)
  end
end
