# frozen_string_literal: true

module Transliteration
  module Model

    def transliteration(name_attr: :name, force_update_on_commit: false)
      mod = Module.new do
        extend ActiveSupport::Concern

        included do
          has_one :entity_name, as: :entity, autosave: true
          delegate :name_en, :name_te, to: :entity_name, prefix: false, allow_nil: true

          define_method :name_attr do
            name_attr
          end

          after_commit do
            Rails.logger.debug("Checking #{self.class.name} with #{id} for update on #{self.name_attr}")
            if force_update_on_commit || saved_change_to_attribute?(self.name_attr)
              Rails.logger.debug("#{self.class.name} with #{id} has #{self.name_attr} updated to #{send(self.name_attr)} enqueued for transliteration")
              TransliterateEntityName.perform_async(self.class.name, id)
            end
          end

          def name_en=(value)
            build_entity_name if entity_name.nil?
            entity_name.name_en = value
          end

          def name_te=(value)
            build_entity_name if entity_name.nil?
            entity_name.name_te = value
          end

          def name_en!(value)
            build_entity_name if entity_name.nil?
            entity_name.name_en = value
            entity_name.save!
          end

          def name_te!(value)
            build_entity_name if entity_name.nil?
            entity_name.name_te = value
            entity_name.save!
          end

          def update_entity_name!
            name = send(self.name_attr)
            return unless name.present?

            build_entity_name if entity_name.nil?

            tt = Transliteration::Text.new("S͜͡u͜͡b͜͡h͜͡a͜͡n͜͡i͜͡")

            entity_name.name_en = tt.transliterate(LANGUAGE_CODE_ENGLISH)
            entity_name.name_te = tt.transliterate(LANGUAGE_CODE_TELUGU)
            #TODO: More languages can be added here
            entity_name.save!

            return unless respond_to?(:reindex)

            Rails.logger.debug("Indexing(mode: queue) #{self.class.name} with #{id} after transliteration")
            reindex(mode: :queue)
          end
        end
      end
      include mod
    end
  end
end
